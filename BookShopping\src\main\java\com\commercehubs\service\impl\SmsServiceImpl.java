package com.commercehubs.service.impl;

import com.commercehubs.service.SmsService;

import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

public class SmsServiceImpl implements SmsService {

    // 存储验证码的Map，key为手机号，value为验证码和过期时间的数组
    private static final Map<String, Object[]> codeMap = new ConcurrentHashMap<>();
    // 验证码有效期（分钟）
    private static final int CODE_EXPIRATION_MINUTES = 5;
    // 发送冷却时间（秒）
    private static final int COOLDOWN_SECONDS = 60;
    // 存储上次发送时间的Map
    private static final Map<String, Long> lastSendTimeMap = new ConcurrentHashMap<>();

    @Override
    public void sendCode(String phone) {
        // 检查是否在冷却时间内
        if (isInCooldown(phone)) {
            throw new RuntimeException("发送太频繁，请稍后再试");
        }

        // 生成6位随机验证码
        String code = generateCode();

        // 存储验证码和过期时间
        long expirationTime = System.currentTimeMillis() + CODE_EXPIRATION_MINUTES * 60 * 1000;
        codeMap.put(phone, new Object[]{code, expirationTime});
        // 记录发送时间
        lastSendTimeMap.put(phone, System.currentTimeMillis());

        // 打印验证码到控制台（仅用于测试）
        System.out.println("模拟发送验证码 - 手机号: " + phone + ", 验证码: " + code);
    }

    @Override
    public boolean validateCode(String phone, String code) {
        Object[] codeInfo = codeMap.get(phone);
        if (codeInfo == null) {
            return false;
        }

        String storedCode = (String) codeInfo[0];
        long expirationTime = (Long) codeInfo[1];

        // 检查是否过期
        if (System.currentTimeMillis() > expirationTime) {
            codeMap.remove(phone);
            return false;
        }

        // 验证通过后删除验证码
        if (storedCode.equals(code)) {
            codeMap.remove(phone);
            return true;
        }

        return false;
    }

    @Override
    public boolean isInCooldown(String phone) {
        Long lastSendTime = lastSendTimeMap.get(phone);
        if (lastSendTime == null) {
            return false;
        }

        return System.currentTimeMillis() - lastSendTime < COOLDOWN_SECONDS * 1000;
    }

    private String generateCode() {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }
}
