package com.commercehubs.service;

/**
 * 发送验证码
 */
public interface SmsService {

    /**
     * 发送验证码
     * @param phone 手机号
     * @throws RuntimeException 发送失败时抛出异常
     */
    void sendCode(String phone);

    /**
     * 验证验证码
     * @param phone 手机号
     * @param code 验证码
     * @return 验证是否通过
     */
    boolean validateCode(String phone, String code);

    /**
     * 检查手机号是否在冷却时间内
     * @param phone 手机号
     * @return 是否在冷却时间内
     */
    boolean isInCooldown(String phone);
}
