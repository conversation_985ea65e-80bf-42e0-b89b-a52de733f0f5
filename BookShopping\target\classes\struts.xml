<!DOCTYPE struts PUBLIC
    "-//Apache Software Foundation//DTD Struts Configuration 2.5//EN"
    "http://struts.apache.org/dtds/struts-2.5.dtd">

<struts>
    <constant name="struts.objectFactory" value="spring"/>
    <constant name="struts.devMode" value="true"/>
    <constant name="struts.action.extension" value=","/>

    <package name="commercehub" extends="struts-default,json-default">

        <!-- 首页Action -->
        <action name="index" class="productAction" method="index">
            <result name="success">/WEB-INF/views/product/index.jsp</result>
            <result name="error">/error.jsp</result>
        </action>

        <!-- 用户相关Action -->
        <action name="user_sendCode" class="userAction" method="sendCode">
            <result type="json">
                <param name="root">result</param>
                <param name="contentType">application/json</param>
                <param name="excludeNullProperties">true</param>
            </result>
        </action>

        <!-- 通过手机号登录注册的相关Action -->
        <action name="user_loginByPhone" class="userAction" method="loginByPhone">
            <result name="success" type="redirectAction">index</result>
            <result name="error">/WEB-INF/views/user/login.jsp</result>
            <result name="index" type="redirectAction">index</result>
            <result name="product_detail" type="redirectAction">
                <param name="actionName">product_detail</param>
                <param name="id">${id}</param>
            </result>
        </action>

        <!-- 登录相关Action -->
        <action name="user_toLogin" class="userAction" method="toLogin">
            <result name="success">/WEB-INF/views/user/login.jsp</result>
            <result name="input">/WEB-INF/views/user/login.jsp</result>
        </action>

        <!-- 商品列表界面等的去登录相关Action -->
        <action name="user_login" class="userAction" method="login">
            <result name="success" type="redirectAction">index</result>
            <result name="error">/WEB-INF/views/user/login.jsp</result>
            <result name="input">/WEB-INF/views/user/login.jsp</result>
            <result name="redirect" type="redirectAction">
                <param name="actionName">${returnUrl}</param>
                <param name="id">${id}</param>
            </result>
        </action>

        <!-- 注册相关Action -->
        <action name="user_register" class="userAction" method="register">
            <result name="success" type="redirectAction">index</result>
            <result name="error">/WEB-INF/views/user/toRegister.jsp</result>
            <result name="input">/WEB-INF/views/user/toRegister.jsp</result>
        </action>

        <!-- 跳转到注册页面Action -->
        <action name="user_toRegister" class="userAction" method="toRegister">
            <result name="success">/WEB-INF/views/user/toRegister.jsp</result>
        </action>

        <!-- 退出相关Action -->
        <action name="user_logout" class="userAction" method="logout">
            <result name="success" type="redirectAction">index</result>
            <result name="error" type="redirectAction">index</result>
        </action>

        <!-- 通配符映射 -->
        <action name="user_*" class="userAction" method="{1}">
            <result name="success">/WEB-INF/views/user/{1}.jsp</result>
            <result name="error">/WEB-INF/views/user/{1}.jsp</result>
        </action>

    </package>
</struts>
