package com.commercehubs.dao.impl;

import com.commercehubs.dao.UserDao;
import com.commercehubs.entity.User;
import org.springframework.stereotype.Repository;

import org.springframework.beans.factory.annotation.Autowired;
import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.persistence.PersistenceContext;
import java.util.List;

@Repository("userDao")
public class UserDaoImpl implements UserDao {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public void save(User user) {
        entityManager.persist(user);
    }

    @Override
    public void update(User user) {
        entityManager.merge(user);
    }

    @Override
    public void delete(Long id) {
        User user = findById(id);
        if (user != null) {
            entityManager.remove(user);
        }
    }

    @Override
    public User findById(Long id) {
        return entityManager.find(User.class, id);
    }

    @Override
    public User findByUsername(String username) {
        TypedQuery<User> query = entityManager.createQuery(
            "SELECT u FROM User u WHERE u.username = :username", User.class);
        query.setParameter("username", username);
        try {
            return query.getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<User> findAll() {
        TypedQuery<User> query = entityManager.createQuery(
            "SELECT u FROM User u", User.class);
        return query.getResultList();
    }

    @Override
    public User findByPhone(String phone) {
        TypedQuery<User> query = entityManager.createQuery(
            "SELECT u FROM User u WHERE u.phone = :phone", User.class);
        query.setParameter("phone", phone);
        try {
            return query.getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public User findByUsernameOrPhone(String username, String phone) {
        TypedQuery<User> query = entityManager.createQuery(
            "SELECT u FROM User u WHERE u.username = :username OR u.phone = :phone", User.class);
        query.setParameter("username", username);
        query.setParameter("phone", phone);
        try {
            return query.getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }
}
