第二部分 登录功能设计
技术栈：Spring + Struts2 + Hibernate + MySQL + Maven
2.1实训目标
- 设计并实现User实体类
- 创建UserDao接口和实现类
- 完成用户数据的基础CRUD操作
- 实现控制层action
2.2实训重点
- 业务逻辑层设计
- 用户手机号唯一性
- JPA（Java Persistence API）注解的使用
- DAO模式的理解
- 数据库操作基础
- 页面跳转逻辑
- 会话管理基础
2.3实训难点
- 实体类字段映射
- 主键生成策略
- 数据库连接异常处理
2.4涉及技能
- Java面向对象编程
- SSH框架
- JPA/Hibernate注解
- SQL基础语法
- 异常处理机制
- 事务管理概念
- 会话管理概念
2.5实训内容
2.5.1 加载依赖库
（1）配置pom.xml
当前实训采用的技术为Spring + Struts2 + Hibernate + MySQL +maven，故需要首先下载相关依赖，配置在pom.xml中
告诉maven这是一个web项目，打包为war	<packaging>war</packaging>

配置版本号	<properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <spring.version>4.3.30.RELEASE</spring.version>
    <struts2.version>2.5.30</struts2.version>
    <hibernate.version>5.4.32.Final</hibernate.version>
</properties>

<dependencies
>中配置相关依赖库	
    <!-- Spring -->
    <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-orm</artifactId>
        <version>${spring.version}</version>
    </dependency>
    <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-tx</artifactId>
        <version>${spring.version}</version>
    </dependency>

	<!-- Struts2 -->
<dependency>
    <groupId>org.apache.struts</groupId>
    <artifactId>struts2-core</artifactId>
    <version>${struts2.version}</version>
</dependency>
<dependency>
    <groupId>org.apache.struts</groupId>
    <artifactId>struts2-spring-plugin</artifactId>
    <version>${struts2.version}</version>
</dependency>
<dependency>
    <groupId>org.apache.struts</groupId>
    <artifactId>struts2-json-plugin</artifactId>
    <version>${struts2.version}</version>
</dependency>

	<!-- Hibernate -->
<dependency>
    <groupId>org.hibernate</groupId>
    <artifactId>hibernate-core</artifactId>
    <version>${hibernate.version}</version>
</dependency>

	<!-- MySQL -->
<dependency>
    <groupId>mysql</groupId>
    <artifactId>mysql-connector-java</artifactId>
    <version>8.0.28</version>
</dependency>
<!-- Servlet & JSP -->
<dependency>
    <groupId>javax.servlet</groupId>
    <artifactId>javax.servlet-api</artifactId>
    <version>4.0.1</version>
    <scope>provided</scope>
</dependency>
<dependency>
    <groupId>javax.servlet.jsp</groupId>
    <artifactId>javax.servlet.jsp-api</artifactId>
    <version>2.3.3</version>
    <scope>provided</scope>
</dependency>
<dependency>
    <groupId>javax.servlet</groupId>
    <artifactId>jstl</artifactId>
    <version>1.2</version>
</dependency>

	 <!-- Caffeine -->
<dependency>
    <groupId>com.github.ben-manes.caffeine</groupId>
    <artifactId>caffeine</artifactId>
    <version>2.8.8</version>
</dependency>

	<!-- Druid -->
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>druid</artifactId>
    <version>1.2.8</version>
</dependency>

Maven WAR插件配置，将依赖正确包含在WAR包中	<build>
    <finalName>commercehubs</finalName>
    <plugins>
        <!-- Maven Compiler Plugin -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>3.8.1</version>
            <configuration>
                <source>1.8</source>
                <target>1.8</target>
                <encoding>UTF-8</encoding>
            </configuration>
        </plugin>

        <!-- Maven WAR Plugin -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-war-plugin</artifactId>
            <version>3.2.3</version>
            <configuration>
                <failOnMissingWebXml>false</failOnMissingWebXml>
                <warSourceDirectory>src/main/webapp</warSourceDirectory>
                <webResources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <targetPath>WEB-INF/classes</targetPath>
                    </resource>
                </webResources>
            </configuration>
        </plugin>
    </plugins>
</build>


（2）通过maven下载依赖
点击m样式的图标，选择生存期\clear,install

（3）同步所有maven项目或重新加载所有maven项目
点击maven界面上的加载图标，进行同步

这时在IDEA左侧的项目的外部库就可以看到下载到的依赖库了
（4）将依赖库进行打包
全选依赖库，右键，选择置于WEB-INF/lib即可，点击应用和确定。

2.5.2 User实体类设计
（1）登录业务场景分析
1）注册业务逻辑：手机号唯一原则
−用户可以直接通过手机号登录注册一体化，但是此时没有用户名及密码。注意点，如果通过手机号码登录时，用户实际上已经注册过，则直接登录无需再注册。故这里需要查询数据库（select：查询）中是否有对应手机号的用户存在。
−用户可以通过常规的注册，包括用户名、密码、手机号码、邮箱。此时如果数据库中有存在（select：查询）同样手机号的用户则更新记录（update：更新），如果没有则创建一条新的数据（insert：插入）。
−用户id为自动生成，唯一。
−创建时间和更新时间：为了便于统计分析，增加这两个字段，为自动生成。
−用户状态status
-- 使用数据库
USE commercehubs;
-- 创建用户表
CREATE TABLE IF NOT EXISTS user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NULL UNIQUE COMMENT '用户名（登录账号）',
    password VARCHAR(100) NULL COMMENT '密码（BCrypt加密存储）',
    email VARCHAR(100) COMMENT '邮箱（用于验证与通知）',
    phone VARCHAR(20) NOT NULL COMMENT '手机号（用于验证与通知）',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    UNIQUE KEY uk_phone(phone)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
（2）创建数据库commercehubs
创建一个init.sql文件来保存sql语句，便于团队沟通。



CREATE DATABASE IF NOT EXISTS commercehubs DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;




（3）创建用户表user
-- 使用数据库
USE commercehubs;

-- 创建用户表
CREATE TABLE IF NOT EXISTS user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NULL UNIQUE COMMENT '用户名（登录账号）',
    password VARCHAR(100) NULL COMMENT '密码（BCrypt加密存储）',
    email VARCHAR(100) COMMENT '邮箱（用于验证与通知）',
    phone VARCHAR(20) NOT NULL COMMENT '手机号（用于验证与通知）',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    UNIQUE KEY uk_phone(phone)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';


点击刷新按钮即可看到创建的user表。点击表后的第三个表格一样的小图标即可查看表内容。

（4）创建软件包com.commercehubs.entity
选中工程中的java文件夹，右键选择新建\软件包

（5）创建实体类User
选择刚创建的软件包，右键，新建\Java类


（6）通过java实现User实体类
采用JPA（Java Persistence API）注解的方式实现。
导入包	package com.commercehubs.entity;（创建类时自动导入的）
import javax.persistence.*;
标记类为 JPA 实体，表示该类会映射到数据库表	@Entity
数据库表映射	@Table(name = "user")
字段映射私有变量	@Id
@GeneratedValue(strategy = GenerationType.IDENTITY)
private Long id;

@Column(name = "username", length = 50, unique = true)
private String username; // 可选，手机号直接注册时可为空

@Column(name = "password", length = 100)
private String password;

@Column(name = "email", length = 100)
private String email;

@Column(name = "phone", length = 20, nullable = false, unique = true)
private String phone;
Getters and Setters	// Getters and Setters
public Long getId() {
    return id;
}

public void setId(Long id) {
    this.id = id;
}

public String getUsername() {
    return username;
}

public void setUsername(String username) {
    this.username = username;
}

public String getPassword() {
    return password;
}

public void setPassword(String password) {
    this.password = password;
}

public String getEmail() {
    return email;
}

public void setEmail(String email) {
    this.email = email;
}

public String getPhone() {
    return phone;
}

public void setPhone(String phone) {
    this.phone = phone;
}

用户名	// 添加一个用于显示的字段
@Transient
private String displayName;

如果是通过手机号登录注册一体，则没有用户名，此时显示部分手机号，如果是常规注册的则登录后显示用户名。	// 获取显示名称
public String getDisplayName() {
    if (displayName != null) {
        return displayName;
    }
    return username != null ? username : phone;
}

public void setDisplayName(String displayName) {
    this.displayName = displayName;
}

无参构造函数，默认也有	public User() {}

（7）maven编译确认没有语法错误
执行clean 和 compile。
2.5.3 Dao接口定义UserDao
（1）创建软件包com.commercehubs.dao
（2）创建接口UserDao
选中软件包，右键，新建\Java类\interface 接口。

注意，选择下来列表中的第二个interface接口



导入包	package com.commercehubs.dao;（创建文件时自动导入的）
import com.commercehubs.entity.User;
import java.util.List;

定义接口	void save(User user);
void update(User user);
void delete(Long id);
User findById(Long id);
User findByUsername(String username);
List<User> findAll();
User findByPhone(String phone);
User findByUsernameOrPhone(String username, String phone);

（3）创建UserDao接口的实现类UserDaoImpl
1）创建软件包com.commercehubs.dao.impl

2）创建java类UserDaoImpl，实现UserDao


实现接口UserDao	package com.commercehubs.dao.impl;
import com.commercehubs.dao.UserDao;

public class UserDaoImpl implements UserDao {
}
	
通过@PersistenceContext 注入EntityManager 	
import com.commercehubs.entity.User;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

public class UserDaoImpl implements UserDao {

@PersistenceContext
private EntityManager entityManager;

@Override
public void save(User user) {
    entityManager.persist(user);
}

@Override
public void update(User user) {
    entityManager.merge(user);
}

@Override
public void delete(Long id) {
    User user = findById(id);
    if (user != null) {
        entityManager.remove(user);
    }
}

}	EntityManager 是 Java Persistence API (JPA) 的核心接口，用于管理数据库操作中的**实体（Entity）**生命周期，包括增删改查（CRUD）和事务管理。它是 JPA 规范中与数据库交互的主要入口。
EntityManager 主要负责：
实体管理：将 Java 对象（POJO）映射到数据库表，并管理其生命周期（新建、持久化、更新、删除、查找）。
数据库操作：执行 CRUD 操作（Create, Read, Update, Delete）。
事务管理：与 EntityTransaction 结合使用，确保数据一致性。
查询支持：提供 JPQL（Java Persistence Query Language）和原生 SQL 查询。

通过用户id查看用户是否存在	@Override
public User findById(Long id) {
    return entityManager.find(User.class, id);
}
	
	@Override
public User findByUsername(String username) {
    TypedQuery<User> query = entityManager.createQuery(
        "SELECT u FROM User u WHERE u.username = :username", User.class);
    query.setParameter("username", username);
    try {
        return query.getSingleResult();
    } catch (Exception e) {
        return null;
    }
}
	
	@Override
public List<User> findAll() {
    TypedQuery<User> query = entityManager.createQuery(
        "SELECT u FROM User u", User.class);
    return query.getResultList();
}
	
	@Override
public User findByPhone(String phone) {
    TypedQuery<User> query = entityManager.createQuery(
        "SELECT u FROM User u WHERE u.phone = :phone", User.class);
    query.setParameter("phone", phone);
    try {
        return query.getSingleResult();
    } catch (Exception e) {
        return null;
    }
}
	
	@Override
public User findByUsernameOrPhone(String username, String phone) {
    TypedQuery<User> query = entityManager.createQuery(
        "SELECT u FROM User u WHERE u.username = :username OR u.phone = :phone", User.class);
    query.setParameter("username", username);
    query.setParameter("phone", phone);
    try {
        return query.getSingleResult();
    } catch (Exception e) {
        return null;
    }
}
	

2.5.4 实现业务service层UserService
（1）发送验证码的模拟功能
创建接口SmsService和实现类SmsServiceImpl。在IDEA的日志中查看虚拟的验证码。
SmsService	/**
 * 发送验证码
 * @param phone 手机号
 * @throws RuntimeException 发送失败时抛出异常
 */
void sendCode(String phone);

/**
 * 验证验证码
 * @param phone 手机号
 * @param code 验证码
 * @return 验证是否通过
 */
boolean validateCode(String phone, String code);

/**
 * 检查手机号是否在冷却时间内
 * @param phone 手机号
 * @return 是否在冷却时间内
 */
boolean isInCooldown(String phone);

SmsServiceImpl	package com.commercehubs.service.impl;
import com.commercehubs.service.SmsService;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class SmsServiceImpl implements SmsService{

    // 存储验证码的Map，key为手机号，value为验证码和过期时间的数组
    private static final Map<String, Object[]> codeMap = new ConcurrentHashMap<>();
    // 验证码有效期（分钟）
    private static final int CODE_EXPIRATION_MINUTES = 5;
    // 发送冷却时间（秒）
    private static final int COOLDOWN_SECONDS = 60;
    // 存储上次发送时间的Map
    private static final Map<String, Long> lastSendTimeMap = new ConcurrentHashMap<>();

    @Override
    public void sendCode(String phone) {
        // 检查是否在冷却时间内
        if (isInCooldown(phone)) {
            throw new RuntimeException("发送太频繁，请稍后再试");
        }

        // 生成6位随机验证码
        String code = generateCode();

        // 存储验证码和过期时间
        long expirationTime = System.currentTimeMillis() + CODE_EXPIRATION_MINUTES * 60 * 1000;
        codeMap.put(phone, new Object[]{code, expirationTime});
        // 记录发送时间
        lastSendTimeMap.put(phone, System.currentTimeMillis());

        // 打印验证码到控制台（仅用于测试）
        System.out.println("模拟发送验证码 - 手机号: " + phone + ", 验证码: " + code);
    }

    @Override
    public boolean validateCode(String phone, String code) {
        Object[] codeInfo = codeMap.get(phone);
        if (codeInfo == null) {
            return false;
        }

        String storedCode = (String) codeInfo[0];
        long expirationTime = (Long) codeInfo[1];

        // 检查是否过期
        if (System.currentTimeMillis() > expirationTime) {
            codeMap.remove(phone);
            return false;
        }

        // 验证通过后删除验证码
        if (storedCode.equals(code)) {
            codeMap.remove(phone);
            return true;
        }

        return false;
    }

    @Override
    public boolean isInCooldown(String phone) {
        Long lastSendTime = lastSendTimeMap.get(phone);
        if (lastSendTime == null) {
            return false;
        }

        return System.currentTimeMillis() - lastSendTime < COOLDOWN_SECONDS * 1000;
    }

    private String generateCode() {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }

}


（2）创建接口UserService
创建软件包com.commercehubs.service

选择软件包com.commercehubs.service，右键，新建\Java类\接口interface。




导入包	package com.commercehubs.service;（创建接口类时自动导入的）
import com.commercehubs.entity.User;
import java.util.List;
业务接口	User register(User user);
User login(String username, String password);
void updateUser(User user);
void deleteUser(Long id);
User getUserById(Long id);
List<User> getAllUsers();
User loginByPhone(String phone, String code);
void sendCode(String phone);


（3）创建实现类UserServiceImpl
创建软件包com.commercehubs.service.impl


创建java类UserServiceImpl实现接口UserService。


导入包	package com.commercehubs.service.impl;

import com.commercehubs.entity.User;
import com.commercehubs.service.SmsService;
import com.commercehubs.service.UserService;
import com.commercehubs.dao.UserDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
	
实现接口UserService 	public class UserServiceImpl implements UserService {
}	
Spring容器管理	@Service
@Transactional
	
依赖注入	@Autowired
private UserDao userDao;

@Autowired
private SmsService smsService;
	
常规注册	@Override
public User register(User user) {
    // 1. 检查手机号是否已被注册
    User existingUserByPhone = userDao.findByPhone(user.getPhone());

    if (existingUserByPhone != null) {
        // 手机号已存在
        if (existingUserByPhone.getUsername() == null || existingUserByPhone.getUsername().isEmpty()) {
            // 这是手机号登录用户在"完善信息"
            existingUserByPhone.setUsername(user.getUsername());
            existingUserByPhone.setPassword(user.getPassword()); // 应该加密
            existingUserByPhone.setEmail(user.getEmail());
            userDao.update(existingUserByPhone);
            return existingUserByPhone; // 返回更新后的用户
        } else {
            // 这是一个完整的账户，手机号冲突
            throw new RuntimeException("该手机号已被注册，您可以直接登录。");
        }
    }

    // 2. 检查用户名是否已被占用（仅在手机号不存在时）
    if (user.getUsername() != null && !user.getUsername().isEmpty()) {
        User existingUserByUsername = userDao.findByUsername(user.getUsername());
        if (existingUserByUsername != null) {
            throw new RuntimeException("该用户名已被占用");
        }
    }
    
    // 3. 创建新用户
    // 在实际项目中，密码应该在这里进行加密处理
    userDao.save(user);
    return user; // 返回新创建的用户
}
	
用户名密码登录	@Override
public User login(String username, String password) {
    User user = userDao.findByUsername(username);
    if (user != null && user.getPassword().equals(password)) {
        return user;
    }
    return null;
}
	
手机号登录，如果没有注册则直接通过手机号注册	@Override
public User loginByPhone(String phone, String code) {
    // 验证码校验
    if (!smsService.validateCode(phone, code)) {
        throw new RuntimeException("验证码错误或已过期");
    }

    // 查找用户，如果不存在则创建
    User user = userDao.findByPhone(phone);
    if (user == null) {
        // 用户不存在，自动注册一个临时账户
        user = new User();
        user.setPhone(phone);
        userDao.save(user);
    }
    return user;
}
	
删改查接口	@Override
public void updateUser(User user) {
    userDao.update(user);
}

@Override
public void deleteUser(Long id) {
    userDao.delete(id);
}

@Override
public User getUserById(Long id) {
    return userDao.findById(id);
}

@Override
public List<User> getAllUsers() {
    return userDao.findAll();
}
	
验证码	@Autowired
private SmsService smsService;

@Override
public void sendCode(String phone) {
    // Implementation of sendCode method
}
	

2.5.5 实现控制层action
（1）创建软件包com.commercehubs.action


（2）创建继承ActionSupport的java类action 




导入包	
Spring框架托管	@Controller("userAction")
public class UserAction extends ActionSupport {
}
依赖注入	@Controller("userAction")
public class UserAction extends ActionSupport {

@Autowired
private UserService userService;

@Autowired
private SmsService smsService;
}
前端登录视图相关的变量	private User user;
private String username;
private String password;
private String phone;
private String code;
private Map<String, Object> result = new HashMap<>();

private Long id;  // 商品ID，用于详情页面返回

前端登录视图相关的变量的Getters and setters，注意这个方法是jsp中元素name的首字母大写	// Getters and setters
public User getUser() {
    return user;
}

public void setUser(User user) {
    this.user = user;
}

public String getUsername() {
    return username;
}

public void setUsername(String username) {
    this.username = username;
}

public String getPassword() {
    return password;
}

public void setPassword(String password) {
    this.password = password;
}

public String getPhone() {
    return phone;
}

public void setPhone(String phone) {
    this.phone = phone;
}

public String getCode() {
    return code;
}

public void setCode(String code) {
    this.code = code;
}

public Map<String, Object> getResult() {
    return result;
}



public Long getId() {
    return id;
}

public void setId(Long id) {
    this.id = id;
}

路由，上一级路由	private String returnUrl;  // 登录成功后的返回URL
路由，返回到上一级路由	public String getReturnUrl() {
    return returnUrl;
}

public void setReturnUrl(String returnUrl) {
    this.returnUrl = returnUrl;
}
简单的通配符配置下的登录、注册路由	public String toLogin() {
    return SUCCESS;
}

public String toRegister() {
    return SUCCESS;
}

约定的常规注册action方法	    public String register() {
        try {
            if (user == null || user.getPhone() == null || user.getPhone().trim().isEmpty()) {
                addActionError("手机号不能为空");
                return ERROR;
            }
            User registeredUser = userService.register(user);

            // 注册成功后自动登录
            if (registeredUser != null) {
                // 将用户信息存入session
                ServletActionContext.getRequest().getSession().setAttribute("user", registeredUser);

//                // 初始化购物车数量
//                int cartItemCount = cartService.getCartItemCount(registeredUser.getId());
//                ServletActionContext.getRequest().getSession().setAttribute("cartItemCount", cartItemCount);
            }

            return SUCCESS;
        } catch (Exception e) {
            addActionError(e.getMessage());
            return ERROR;
        }
    }

常规登录	    public String login() {
        try {
            // 输入验证
            if (user == null ||
                    user.getUsername() == null || user.getUsername().trim().isEmpty() ||
                    user.getPassword() == null || user.getPassword().trim().isEmpty()) {
                addActionError("用户名和密码不能为空");
                return INPUT;
            }

            User loginUser = userService.login(user.getUsername(), user.getPassword());
            if (loginUser != null) {
                // 将用户信息存入session
                ServletActionContext.getRequest().getSession().setAttribute("user", loginUser);

                // 初始化购物车数量
//                int cartItemCount = cartService.getCartItemCount(loginUser.getId());
//                ServletActionContext.getRequest().getSession().setAttribute("cartItemCount", cartItemCount);

                if (returnUrl != null && !returnUrl.trim().isEmpty()) {
                    return "redirect";
                }
                return SUCCESS;
            } else {
                addActionError("用户名或密码错误");
                return ERROR;
            }
        } catch (Exception e) {
            addActionError("登录失败：" + e.getMessage());
            return ERROR;
        }
    }

通过手机号登录	    public String loginByPhone() {
        try {
            if (phone == null || phone.trim().isEmpty()) {
                addActionError("手机号不能为空");
                return ERROR;
            }
            if (code == null || code.trim().isEmpty()) {
                addActionError("验证码不能为空");
                return ERROR;
            }

            user = userService.loginByPhone(phone, code);
            if (user != null) {
                // 登录成功，将用户信息放入session
                org.apache.struts2.ServletActionContext.getRequest().getSession().setAttribute("user", user);

                // 初始化购物车数量
//                int cartItemCount = cartService.getCartItemCount(user.getId());
//                ServletActionContext.getRequest().getSession().setAttribute("cartItemCount", cartItemCount);

                // 处理返回URL
                if (returnUrl != null && !returnUrl.trim().isEmpty()) {
                    if ("product_detail".equals(returnUrl) && id != null) {
                        // 如果是从商品详情页来的，需要带上商品ID返回
                        return "product_detail";
                    }
                    return returnUrl;
                }
                return SUCCESS;
            } else {
                addActionError("验证码错误或已过期");
                return ERROR;
            }
        } catch (Exception e) {
            addActionError(e.getMessage());
            return ERROR;
        }
    }

发送验证码	public String sendCode() {
    try {
        if (phone == null || phone.trim().isEmpty()) {
            result.put("success", false);
            result.put("message", "手机号不能为空");
            return SUCCESS;
        }
        if (!phone.matches("^1[3-9]\\d{9}$")) {
            result.put("success", false);
            result.put("message", "请输入正确的手机号");
            return SUCCESS;
        }
        smsService.sendCode(phone);
        result.put("success", true);
        result.put("message", "验证码发送成功");
    } catch (Exception e) {
        result.put("success", false);
        result.put("message", e.getMessage());
    }
    return SUCCESS;
}

退出登录	public String logout() {
    try {
        // 清除session中的用户信息
        ServletActionContext.getRequest().getSession().removeAttribute("user");
        ServletActionContext.getRequest().getSession().removeAttribute("cartItemCount");
        // 或者直接使session失效
        // ServletActionContext.getRequest().getSession().invalidate();
        return SUCCESS;
    } catch (Exception e) {
        addActionError("退出失败：" + e.getMessage());
        return ERROR;
    }
}


1）导入类，将鼠标放置在变量上，点击“导入类”即可自动导入类。

2）表单中的数据封装在HTTP请求参数中，自动填充(Parameter Population) 或 模型驱动 (Model-Driven) action中的私有变量
在 JSP 中写的这行代码：<s:form action="user_login" method="post" theme="simple">当用户提交这个表单时，会发生以下一系列事情：
Action 映射: Struts2 会首先查看 struts.xml 配置文件找到到 name="user_login" 的 <action> 配置。
    <!-- 在 struts.xml 中 -->
    <action name="user_login" class="userAction" method="login">
        ...
    </action>
这个配置告诉 Struts2，user_login 这个 action 应该由 userAction 这个 Bean（也就是 UserAction.java 这个类）来处理。
参数拦截器 (Parameter Interceptor): 在 UserAction 的 login() 方法执行之前，请求会经过一系列的“拦截器”，其中最重要的一个就是 params 拦截器。
自动填充/绑定: params 拦截器的工作就是：
获取所有从表单提交过来的请求参数（比如 user.username, user.password）。
查看 UserAction 类，找到这些参数对应的 public 的 setter 方法。
通过 Java 的反射机制，调用这些 setter 方法，将表单中的值赋给 UserAction 类的私有成员变量。

（3）创建ProductAction的java类action（未具体实现，这里只是用于首页显示占位）

package com.commercehubs.action;

import com.opensymphony.xwork2.ActionSupport;

public class ProductAction extends ActionSupport {

    public String index() {
        return SUCCESS;
    }
}


2.5.6 实现视图JSP
（1）创建首页webapp/WEB-INF/product/index.jsp
在webapp目录下创建默认首页index.jsp，以便重定向到webapp/WEB-INF/product/index.jsp。


<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%-- 自动转发到index action --%>
<% response.sendRedirect(request.getContextPath() + "/index"); %> 
（2）在webapp/WEB-INF 下创建视图目录views，用户不能直接访问




（3）在views目录下创建product的首页index.jsp


（4）在webapp/WEB-INF 下创建视图目录user，用户不能直接访问


（5）在user目录下创建login.jsp界面


（6）在user目录下创建loginByPhone.jsp界面

（7）在user目录下创建toRegister.jsp

2.5.7 Spring配置文件applicationContext-dev.xml
在resources文件夹下创建文件applicationContext-dev.xml

	<!-- 数据源配置 -->
<bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
    <property name="driverClassName" value="com.mysql.cj.jdbc.Driver"/>
    <property name="url" value="****************************************************************************"/>
    <property name="username" value="root"/>
    <property name="password" value="admin123"/>
    <property name="initialSize" value="0"/>
    <property name="minIdle" value="0"/>
    <property name="maxActive" value="20"/>
    <property name="maxWait" value="60000"/>
    <property name="timeBetweenEvictionRunsMillis" value="60000"/>
    <property name="minEvictableIdleTimeMillis" value="300000"/>
    <property name="validationQuery" value="SELECT 1"/>
    <property name="testWhileIdle" value="true"/>
    <property name="testOnBorrow" value="false"/>
    <property name="testOnReturn" value="false"/>
    <property name="poolPreparedStatements" value="true"/>
    <property name="maxPoolPreparedStatementPerConnectionSize" value="20"/>
</bean>

	<!-- JPA EntityManagerFactory配置 -->
<bean id="entityManagerFactory" class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean">
    <property name="dataSource" ref="dataSource"/>
    <property name="packagesToScan" value="com.commercehub.entity"/>
    <property name="jpaVendorAdapter">
        <bean class="org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter">
            <property name="databasePlatform" value="org.hibernate.dialect.MySQL5InnoDBDialect"/>
            <property name="showSql" value="true"/>
            <property name="generateDdl" value="true"/>
        </bean>
    </property>
    <property name="jpaProperties">
        <props>
            <prop key="hibernate.dialect">org.hibernate.dialect.MySQL5InnoDBDialect</prop>
            <prop key="hibernate.show_sql">true</prop>
            <prop key="hibernate.format_sql">true</prop>
            <prop key="hibernate.hbm2ddl.auto">update</prop>
            <prop key="hibernate.connection.characterEncoding">utf8</prop>
            <prop key="hibernate.connection.useUnicode">true</prop>
        </props>
    </property>
</bean>

	<!-- 事务管理器配置 -->
<bean id="transactionManager" class="org.springframework.orm.jpa.JpaTransactionManager">
    <property name="entityManagerFactory" ref="entityManagerFactory"/>
</bean>

<!-- 启用事务注解 -->
<tx:annotation-driven transaction-manager="transactionManager"/>


2.5.8 路由配置struts.xml
（1）在resources目录下创建struts.xml



	<!DOCTYPE struts PUBLIC
        "-//Apache Software Foundation//DTD Struts Configuration 2.5//EN"
        "http://struts.apache.org/dtds/struts-2.5.dtd">

<struts>
    <constant name="struts.objectFactory" value="spring"/>
    <constant name="struts.devMode" value="true"/>
    <constant name="struts.action.extension" value=","/>

    <package name="commercehub" extends="struts-default,json-default">
</struts>

<!--        ===================路由映射：===============================-->
<!--        这里的每一项都是明确指定的。更重要的是，它定义了多个、不同类型的返回结果：-->
<!--        如果login()方法返回"success"，它会重定向到index这个Action。-->
<!--        如果返回"error"或"input"，它会转发到error.jsp或者login.jsp页面。-->
<!--        如果返回"redirect"，它还会根据参数重定向到另一个动态的Action。-->
<!--        对于处理复杂的、不符合约定的逻辑，通配符映射完全做不到的。通配符映射里只定义了简单的success和error，并且都是直接转发到JSP页面。-->
	<!-- 首页Action -->
<action name="index" class="productAction" method="index">
    <result name="success">/WEB-INF/views/product/index.jsp</result>
    <result name="error">/error.jsp</result>
</action>

	<!-- 用户相关Action -->
<action name="user_sendCode" class="userAction" method="sendCode">
    <result type="json">
        <param name="root">result</param>
        <param name="contentType">application/json</param>
        <param name="excludeNullProperties">true</param>
    </result>
</action>

	<!-- 通过手机号登录注册的相关Action -->
<action name="user_loginByPhone" class="userAction" method="loginByPhone">
    <result name="success" type="redirectAction">index</result>
    <result name="error">/WEB-INF/views/user/login.jsp</result>
    <result name="index" type="redirectAction">index</result>
    <result name="product_detail" type="redirectAction">
        <param name="actionName">product_detail</param>
        <param name="id">${id}</param>
    </result>
</action>

	<!-- 登录相关Action -->
<action name="user_toLogin" class="userAction" method="toLogin">
    <result name="success">/WEB-INF/views/user/login.jsp</result>
    <result name="input">/WEB-INF/views/user/login.jsp</result>
</action>

	<!-- 商品列表界面等的去登录相关Action -->
<action name="user_login" class="userAction" method="login">
    <result name="success" type="redirectAction">index</result>
    <result name="error">/WEB-INF/views/user/login.jsp</result>
    <result name="input">/WEB-INF/views/user/login.jsp</result>
    <result name="redirect" type="redirectAction">
        <param name="actionName">${returnUrl}</param>
        <param name="id">${id}</param>
    </result>
</action>

	<!-- 注册相关Action -->
<action name="user_register" class="userAction" method="register">
    <result name="success" type="redirectAction">index</result>
    <result name="error">/WEB-INF/views/user/toRegister.jsp</result>
    <result name="input">/WEB-INF/views/user/toRegister.jsp</result>
</action>

	<!-- 跳转到注册页面Action -->
<action name="user_toRegister" class="userAction" method="toRegister">
    <result name="success">/WEB-INF/views/user/toRegister.jsp</result>
</action>

	<!-- 退出相关Action -->
<action name="user_logout" class="userAction" method="logout">
    <result name="success" type="redirectAction">index</result>
    <result name="error" type="redirectAction">index</result>
</action>

<!--        通配符*映射：-->
<!--        这是一个使用了通配符的映射。它意味着，任何以 user_ 开头的、并且后面跟着某个名字（比如 toRegister）的Action，都会被 UserAction 这个类来处理。method="{1}" 表示调用的方法名就是通配符 * 匹配到的那部分。-->
<!--        所以，当我们访问 user_toRegister 时，它会执行 UserAction 的 toRegister() 方法。-->
<!--        而 <result name="success">/WEB-INF/views/user/{1}.jsp</result> 则告诉我们，如果 toRegister() 方法返回 SUCCESS，Struts2会去寻找 /WEB-INF/views/user/toRegister.jsp 这个文件。-->
<!--        适用场景：-->
<!--        它非常适合那些逻辑简单的"跳转"类Action，比如：请求 user_toRegister 就调用 toRegister() 方法，然后返回 toRegister.jsp 页面。整个流程的名字都是一致的，符合约定。-->
	<action name="user_*" class="userAction" method="{1}">
    <result name="success">/WEB-INF/views/user/{1}.jsp</result>
    <result name="error">/WEB-INF/views/user/{1}.jsp</result>
</action>


2.5.9 配置web.xml
	<!-- 开发环境使用 applicationContext-dev.xml，生产环境使用 applicationContext-prod.xml -->
<!-- 切换时只需修改 param-value 即可 -->
<context-param>
    <param-name>contextConfigLocation</param-name>
    <param-value>classpath:applicationContext-dev.xml</param-value>
    <!-- 生产环境请改为：classpath:applicationContext-prod.xml -->
</context-param>

	<!-- Spring监听器 -->
<listener>
    <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
</listener>

	    <!-- Struts2过滤器 -->
    <filter>
        <filter-name>struts2</filter-name>
<!--        Struts2 的核心过滤器 StrutsPrepareAndExecuteFilter，负责处理 Struts2 的请求分发和执行。-->
        <filter-class>org.apache.struts2.dispatcher.filter.StrutsPrepareAndExecuteFilter</filter-class>
        <init-param>
<!--            过滤器的初始化参数，这里配置了一个排除模式：
                正则表达式，匹配所有以 /static/ 开头的 URL（如 /static/css/style.css），
                这些请求会直接绕过 Struts2，通常用于静态资源（CSS、JS、图片等）的快速访问。-->
            <param-name>struts.action.excludePattern</param-name>
            <param-value>^/static/.*</param-value>
        </init-param>
    </filter>

	<!--    定义过滤器的 URL 映射-->
    <filter-mapping>
        <!-- 引用上面定义的过滤器 -->
        <filter-name>struts2</filter-name>
        <!-- /* 表示拦截所有请求（包括静态资源和动态请求），
        但由于 <init-param> 中配置了 excludePattern，实际 /static/ 开头的请求会被排除。 -->
        <url-pattern>/*</url-pattern>
    </filter-mapping>

	<!-- 字符编码过滤器 -->
<filter>
    <filter-name>characterEncodingFilter</filter-name>
    <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
    <init-param>
        <param-name>encoding</param-name>
        <param-value>UTF-8</param-value>
    </init-param>
</filter>
<filter-mapping>
    <filter-name>characterEncodingFilter</filter-name>
    <url-pattern>/*</url-pattern>
</filter-mapping>

	<!-- 配置欢迎页面 -->
<welcome-file-list>
    <welcome-file>index.jsp</welcome-file>
</welcome-file-list>

2.5.10 问题
（1）Spring Web依赖没有被正确包含在WAR包中。问题是在applicationContext-dev.xml中缺少了tx命名空间的声明。
严重: Error configuring application listener of class org.springframework.web.context.ContextLoaderListener
java.lang.ClassNotFoundException: org.springframework.web.context.ContextLoaderListener
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1892)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1735)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:495)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:477)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:113)
	at org.apache.catalina.core.StandardContext.listenerStart(StandardContext.java:5026)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5633)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:145)
	at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:1015)
	at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:991)
	at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:652)
	at org.apache.catalina.startup.HostConfig.manageApp(HostConfig.java:1899)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.tomcat.util.modeler.BaseModelMBean.invoke(BaseModelMBean.java:301)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at org.apache.catalina.mbeans.MBeanFactory.createStandardContext(MBeanFactory.java:618)
	at org.apache.catalina.mbeans.MBeanFactory.createStandardContext(MBeanFactory.java:565)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.tomcat.util.modeler.BaseModelMBean.invoke(BaseModelMBean.java:301)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at com.sun.jmx.remote.security.MBeanServerAccessController.invoke(MBeanServerAccessController.java:468)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1408)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
解决方案：增加xmlns:tx="http://www.springframework.org/schema/tx"声明
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context.xsd
           http://www.springframework.org/schema/tx
           http://www.springframework.org/schema/tx/spring-tx.xsd">

（2）问题：缺少Spring Web依赖没有被正确包含在WAR包中。问题是在部署时，Spring的jar包没有被正确打包到WAR文件中。
java.lang.ClassNotFoundException: org.springframework.web.context.ContextLoaderListener
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1892)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1735)
	at org.apache.catalina.util.Introspection.loadClass(Introspection.java:143)
	at org.apache.catalina.startup.WebAnnotationSet.loadApplicationListenerAnnotations(WebAnnotationSet.java:82)
	at org.apache.catalina.startup.WebAnnotationSet.loadApplicationAnnotations(WebAnnotationSet.java:63)
	at org.apache.catalina.startup.ContextConfig.applicationAnnotationsConfig(ContextConfig.java:417)
	at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:891)
	at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:388)
	at org.apache.catalina.util.LifecycleSupport.fireLifecycleEvent(LifecycleSupport.java:117)
	at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:90)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5519)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:145)
	at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:1015)
	at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:991)
	at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:652)
	at org.apache.catalina.startup.HostConfig.manageApp(HostConfig.java:1899)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.tomcat.util.modeler.BaseModelMBean.invoke(BaseModelMBean.java:301)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at org.apache.catalina.mbeans.MBeanFactory.createStandardContext(MBeanFactory.java:618)
	at org.apache.catalina.mbeans.MBeanFactory.createStandardContext(MBeanFactory.java:565)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.tomcat.util.modeler.BaseModelMBean.invoke(BaseModelMBean.java:301)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at com.sun.jmx.remote.security.MBeanServerAccessController.invoke(MBeanServerAccessController.java:468)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1408)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

六月 22, 2025 5:15:02 上午 org.apache.catalina.util.Introspection loadClass
详细: Failed to load class [org.springframework.web.filter.CharacterEncodingFilter]
java.lang.ClassNotFoundException: org.springframework.web.filter.CharacterEncodingFilter
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1892)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1735)
	at org.apache.catalina.util.Introspection.loadClass(Introspection.java:143)
	at org.apache.catalina.startup.WebAnnotationSet.loadApplicationFilterAnnotations(WebAnnotationSet.java:102)
	at org.apache.catalina.startup.WebAnnotationSet.loadApplicationAnnotations(WebAnnotationSet.java:64)
	at org.apache.catalina.startup.ContextConfig.applicationAnnotationsConfig(ContextConfig.java:417)
	at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:891)
	at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:388)
	at org.apache.catalina.util.LifecycleSupport.fireLifecycleEvent(LifecycleSupport.java:117)
	at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:90)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5519)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:145)
	at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:1015)
	at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:991)
	at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:652)
	at org.apache.catalina.startup.HostConfig.manageApp(HostConfig.java:1899)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.tomcat.util.modeler.BaseModelMBean.invoke(BaseModelMBean.java:301)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at org.apache.catalina.mbeans.MBeanFactory.createStandardContext(MBeanFactory.java:618)
	at org.apache.catalina.mbeans.MBeanFactory.createStandardContext(MBeanFactory.java:565)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.tomcat.util.modeler.BaseModelMBean.invoke(BaseModelMBean.java:301)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at com.sun.jmx.remote.security.MBeanServerAccessController.invoke(MBeanServerAccessController.java:468)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1408)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
解决方案：问题在于缺少Maven WAR插件配置，这导致依赖没有被正确打包到WAR文件中。添加必要的构建配置：
<build>
    <finalName>commercehubs</finalName>
    <plugins>
        <!-- Maven Compiler Plugin -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>3.8.1</version>
            <configuration>
                <source>1.8</source>
                <target>1.8</target>
                <encoding>UTF-8</encoding>
            </configuration>
        </plugin>

        <!-- Maven WAR Plugin -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-war-plugin</artifactId>
            <version>3.2.3</version>
            <configuration>
                <failOnMissingWebXml>false</failOnMissingWebXml>
                <warSourceDirectory>src/main/webapp</warSourceDirectory>
                <webResources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <targetPath>WEB-INF/classes</targetPath>
                    </resource>
                </webResources>
            </configuration>
        </plugin>
    </plugins>
</build>
（3）问题：Spring无法找到UserDao的bean。问题在于UserDao接口没有被正确配置为Spring bean。
严重: Exception sending context initialized event to listener instance of class org.springframework.web.context.ContextLoaderListener
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userDao'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.commercehubs.dao.UserDao' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:588)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:87)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessPropertyValues(AutowiredAnnotationBeanPostProcessor.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1257)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:551)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:481)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:312)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:308)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:756)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:867)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:542)
	at org.springframework.web.context.ContextLoader.configureAndRefreshWebApplicationContext(ContextLoader.java:443)
	at org.springframework.web.context.ContextLoader.initWebApplicationContext(ContextLoader.java:325)
	at org.springframework.web.context.ContextLoaderListener.contextInitialized(ContextLoaderListener.java:107)
	at org.apache.catalina.core.StandardContext.listenerStart(StandardContext.java:5110)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5633)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:145)
	at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:1015)
	at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:991)
	at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:652)
	at org.apache.catalina.startup.HostConfig.manageApp(HostConfig.java:1899)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.tomcat.util.modeler.BaseModelMBean.invoke(BaseModelMBean.java:301)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at org.apache.catalina.mbeans.MBeanFactory.createStandardContext(MBeanFactory.java:618)
	at org.apache.catalina.mbeans.MBeanFactory.createStandardContext(MBeanFactory.java:565)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.tomcat.util.modeler.BaseModelMBean.invoke(BaseModelMBean.java:301)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at com.sun.jmx.remote.security.MBeanServerAccessController.invoke(MBeanServerAccessController.java:468)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at java.security.AccessController.doPrivileged(Native Method)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1408)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.commercehubs.dao.UserDao' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:1489)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1100)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1062)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:585)
	... 62 more
解决方案：给UserDaoImpl类增加Spring注解@Repository("userDao")

（4）问题：

（5）问题：

2.5.11 运行界面
（1）首页

（2）通过手机号登录







（3）未有手机号注册




（4）已登录过的手机号注册



（5）用户名密码登录



（6）退出
